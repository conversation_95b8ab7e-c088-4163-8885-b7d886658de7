"""Integration tests for the stats and ps CLI commands."""

from __future__ import annotations

import subprocess
import sys

import pytest


def test_stats_command_basic() -> None:
    """Test stats command executes successfully."""
    result = subprocess.run(
        [sys.executable, "-m", "skaha.cli.main", "stats", "--help"],
        capture_output=True,
        text=True,
        timeout=30,
    )
    assert result.returncode == 0


def test_ps_command_basic() -> None:
    """Test ps command executes successfully."""
    result = subprocess.run(
        [sys.executable, "-m", "skaha.cli.main", "ps", "--help"],
        capture_output=True,
        text=True,
        timeout=30,
    )
    assert result.returncode == 0


@pytest.mark.slow
def test_stats_command_integration() -> None:
    """Test stats command integration (may fail without proper auth/config)."""
    result = subprocess.run(
        [sys.executable, "-m", "skaha.cli.main", "stats"],
        capture_output=True,
        text=True,
        timeout=30,
    )
    # Command should exit cleanly even if it fails due to auth/config issues
    # We're just testing that it doesn't crash with exit code 2 (syntax error)
    assert result.returncode in [0, 1]  # 0 = success, 1 = expected failure


@pytest.mark.slow
def test_ps_command_integration() -> None:
    """Test ps command integration (may fail without proper auth/config)."""
    result = subprocess.run(
        [sys.executable, "-m", "skaha.cli.main", "ps"],
        capture_output=True,
        text=True,
        timeout=30,
    )
    # Command should exit cleanly even if it fails due to auth/config issues
    # We're just testing that it doesn't crash with exit code 2 (syntax error)
    assert result.returncode in [0, 1]  # 0 = success, 1 = expected failure
