"""Tests for the stats and ps CLI commands."""

from __future__ import annotations

from datetime import datetime, timezone
from unittest.mock import AsyncMock, patch

import pytest
from typer.testing import CliRunner

from skaha.cli.ps import ps
from skaha.cli.stats import stats

runner = CliRunner()


def test_stats_command_basic() -> None:
    """Test stats command executes successfully."""
    with patch("skaha.cli.stats.AsyncSession") as mock_session_class:
        # Setup mock
        mock_session = AsyncMock()
        mock_session.stats.return_value = {
            "instances": {"running": 5, "pending": 2, "total": 7},
            "cores": {"inUse": 20, "available": 100, "maxCPUCores": {"cpuCores": 50}},
            "ram": {"inUse": "40Gi", "available": "200Gi", "maxRAM": {"ram": "100Gi"}}
        }
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session_class.return_value.__aexit__.return_value = None
        
        # Test command
        result = runner.invoke(stats, [])
        assert result.exit_code == 0


def test_stats_command_with_debug() -> None:
    """Test stats command with debug flag."""
    with patch("skaha.cli.stats.AsyncSession") as mock_session_class:
        # Setup mock
        mock_session = AsyncMock()
        mock_session.stats.return_value = {
            "instances": {"running": 5, "pending": 2, "total": 7},
            "cores": {"inUse": 20, "available": 100, "maxCPUCores": {"cpuCores": 50}},
            "ram": {"inUse": "40Gi", "available": "200Gi", "maxRAM": {"ram": "100Gi"}}
        }
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session_class.return_value.__aexit__.return_value = None
        
        # Test command with debug flag
        result = runner.invoke(stats, ["--debug"])
        assert result.exit_code == 0


def test_ps_command_basic() -> None:
    """Test ps command executes successfully."""
    with patch("skaha.cli.ps.AsyncSession") as mock_session_class:
        # Setup mock
        mock_session = AsyncMock()
        mock_session.fetch.return_value = [
            {
                "id": "session-123",
                "name": "test-session",
                "type": "notebook",
                "status": "Running",
                "image": "jupyter/scipy-notebook:latest",
                "startTime": datetime.now(timezone.utc).isoformat(),
                "userid": "testuser",
                "runAsUID": 1000,
                "runAsGID": 1000,
                "supplementalGroups": [],
                "connectURL": "https://example.com/session-123",
                "requestedRAM": "2Gi",
                "requestedCPUCores": 1.0,
                "requestedGPUCores": 0
            }
        ]
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session_class.return_value.__aexit__.return_value = None
        
        # Test command
        result = runner.invoke(ps, [])
        assert result.exit_code == 0


def test_ps_command_with_all_flag() -> None:
    """Test ps command with --all flag."""
    with patch("skaha.cli.ps.AsyncSession") as mock_session_class:
        # Setup mock
        mock_session = AsyncMock()
        mock_session.fetch.return_value = []
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session_class.return_value.__aexit__.return_value = None
        
        # Test command with --all flag
        result = runner.invoke(ps, ["--all"])
        assert result.exit_code == 0


def test_ps_command_with_quiet_flag() -> None:
    """Test ps command with --quiet flag."""
    with patch("skaha.cli.ps.AsyncSession") as mock_session_class:
        # Setup mock
        mock_session = AsyncMock()
        mock_session.fetch.return_value = [
            {
                "id": "session-123",
                "name": "test-session",
                "type": "notebook",
                "status": "Running",
                "image": "jupyter/scipy-notebook:latest",
                "startTime": datetime.now(timezone.utc).isoformat(),
                "userid": "testuser",
                "runAsUID": 1000,
                "runAsGID": 1000,
                "supplementalGroups": [],
                "connectURL": "https://example.com/session-123",
                "requestedRAM": "2Gi",
                "requestedCPUCores": 1.0,
                "requestedGPUCores": 0
            }
        ]
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session_class.return_value.__aexit__.return_value = None
        
        # Test command with --quiet flag
        result = runner.invoke(ps, ["--quiet"])
        assert result.exit_code == 0


def test_ps_command_with_debug_flag() -> None:
    """Test ps command with --debug flag."""
    with patch("skaha.cli.ps.AsyncSession") as mock_session_class:
        # Setup mock
        mock_session = AsyncMock()
        mock_session.fetch.return_value = []
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session_class.return_value.__aexit__.return_value = None
        
        # Test command with --debug flag
        result = runner.invoke(ps, ["--debug"])
        assert result.exit_code == 0


def test_ps_command_with_kind_filter() -> None:
    """Test ps command with --kind filter."""
    with patch("skaha.cli.ps.AsyncSession") as mock_session_class:
        # Setup mock
        mock_session = AsyncMock()
        mock_session.fetch.return_value = []
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session_class.return_value.__aexit__.return_value = None
        
        # Test command with --kind filter
        result = runner.invoke(ps, ["--kind", "notebook"])
        assert result.exit_code == 0


def test_ps_command_with_status_filter() -> None:
    """Test ps command with --status filter."""
    with patch("skaha.cli.ps.AsyncSession") as mock_session_class:
        # Setup mock
        mock_session = AsyncMock()
        mock_session.fetch.return_value = []
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session_class.return_value.__aexit__.return_value = None
        
        # Test command with --status filter
        result = runner.invoke(ps, ["--status", "Running"])
        assert result.exit_code == 0


def test_ps_command_empty_sessions() -> None:
        result = runner.invoke(ps, [])
        assert result.exit_code == 0
